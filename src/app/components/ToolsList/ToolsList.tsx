import Link from "next/link";
import GenericButton from "../GenericButton/GenericButton";

export default function ToolsList() {
  return (
    <>
      <h2 className="pl-4 mb-8 text-3xl font-bold">All Recon Tools</h2>
      <Link href="/tools/sandbox" className="m-0 p-0 text-center w-full mb-3">
        <GenericButton className={"button_login m-0 p-0 w-full"}>
          Scaffold invariants Sandbox
        </GenericButton>
      </Link>
      <h3 className="pl-4 mb-4 text-xl">Log Parsers</h3>
      <Link href="/tools/medusa" className="m-0 p-0 text-center w-full mb-3">
        <GenericButton className={"button_login m-0 p-0 w-full"}>
          Medusa Log to Foundry
        </GenericButton>
      </Link>
      <Link href="/tools/echidna" className="m-0 p-0 text-center w-full mb-3">
        <GenericButton className={"button_login m-0 p-0 w-full"}>
          Echidna Log to Foundry
        </GenericButton>
      </Link>
      <Link href="/tools/halmos" className="m-0 p-0 text-center w-full mb-3">
        <GenericButton className={"button_login m-0 p-0 w-full"}>
          Halmos Log to Foundry
        </GenericButton>
      </Link>
      <h3 className="pl-4 mb-4 text-xl">Tools</h3>
      <Link
        href="/tools/bytecode-compare"
        className="m-0 p-0 text-center w-full mb-3"
      >
        <GenericButton className={"button_login m-0 p-0 w-full"}>
          Bytecode compare
        </GenericButton>
      </Link>
      <Link
        href="/tools/bytecode-to-interface"
        className="m-0 p-0 text-center w-full mb-3"
      >
        <GenericButton className={"button_login m-0 p-0 w-full"}>
          Bytecode to interface
        </GenericButton>
      </Link>
    </>
  );
}
