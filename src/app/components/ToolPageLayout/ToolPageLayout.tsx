"use client";
import Link from "next/link";
import { useState } from "react";

import type { ENV_TYPE } from "@/app/app.constants";
import { AppInput } from "@/app/components/app-input";
import { AppLogo } from "@/app/components/app-logo";
import { AppPageTitle } from "@/app/components/app-page-title";
import { AppTextarea } from "@/app/components/app-textarea";
import LogComponent from "@/app/components/LogComponent/LogComponent";
import Footer from "@/app/components/Footer/Footer";
import VideoPlayer from "@/app/components/VideoPlayer/VideoPlayer";

interface ToolPageLayoutProps {
  toolType: ENV_TYPE;
  toolName: string;
  toolDescription: string[];
  youtubeUrl?: string;
  youtubeOverlayText?: string;
}

export default function ToolPageLayout({
  toolType,
  toolName,
  toolDescription,
  youtubeUrl,
  youtubeOverlayText,
}: ToolPageLayoutProps) {
  const [logs, setLogs] = useState("");
  const [prefix, setPrefix] = useState("");

  return (
    <div>
      <div className="main-container w-full overflow-x-hidden">
        <div className="absolute inset-0 z-0 bg-gradient-to-b from-transparent via-transparent to-black">
          <div
            className="size-full"
            style={{
              backgroundImage: `linear-gradient(0deg, #5C25D2, #5C25D2), url('/bg-recon.jpeg')`,
              backgroundBlendMode: "color, normal",
              backgroundSize: "cover",
              backgroundPosition: "center",
            }}
          />
        </div>

        <div className="relative z-10 min-h-screen">
          <div className="gradient-dark-bg flex items-center justify-between bg-blockBg px-[40px] py-[20px]">
            <Link href="/dashboard" className="cursor-pointer">
              <AppLogo />
            </Link>
          </div>

          <div className="flex min-h-[calc(100vh-80px)] items-center justify-center px-4 py-8">
            <div className="w-full max-w-4xl">
              <div className="mb-8 text-center">
                <AppPageTitle className="mb-4 text-white">
                  {toolName}
                </AppPageTitle>

                {toolDescription.map((desc, index) => (
                  <p
                    key={`desc-${desc.slice(0, 20)}-${index}`}
                    className="mb-3 text-[18px] leading-[22px] text-gray-300"
                  >
                    {desc}
                  </p>
                ))}
              </div>

              {youtubeUrl && (
                <div className="mb-8 flex justify-center">
                  <div className="w-full max-w-2xl">
                    <VideoPlayer
                      link={youtubeUrl}
                      overlayText={youtubeOverlayText}
                    />
                  </div>
                </div>
              )}

              <div className="mx-auto max-w-2xl rounded-lg bg-black/30 p-8 backdrop-blur-sm">
                <AppInput
                  className="mb-6"
                  label="Add a Prefix for your convenience"
                  value={prefix}
                  onChange={(e) => setPrefix(e.target.value)}
                  type="text"
                />

                <AppTextarea
                  className="mb-6"
                  label={`Paste ${toolName.split(" ")[0]} Logs Here`}
                  value={logs}
                  onChange={(e) => setLogs(e.target.value)}
                  type="text"
                />

                <LogComponent fuzzer={toolType} logs={logs} prefix={prefix} />
              </div>
            </div>
          </div>

          <Footer />
        </div>
      </div>
    </div>
  );
}
